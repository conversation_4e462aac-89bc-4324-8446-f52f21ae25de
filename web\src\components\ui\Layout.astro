---
export interface Props {
  title: string;
  description?: string;
  lang?: string;
}

const { title, description, lang = "es" } = Astro.props;
---

<html lang={lang}>
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content={description || "Audioguías murales Santa Marta"} />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    <title>{title}</title>
    
    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  </head>
  <body class="font-sans bg-gray-50 text-gray-900">
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-md z-50">
      Saltar al contenido principal
    </a>
    
    <slot />
  </body>
</html>

<style>
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  /* Estilos para alto contraste */
  @media (prefers-contrast: high) {
    body {
      @apply bg-white text-black;
    }
  }
  
  /* Reducir movimiento si el usuario lo prefiere */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
</style> 