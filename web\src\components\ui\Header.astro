---
import contentEs from '../../data/content-es.json';
// import { LanguageSelector } from '../react/LanguageSelector';

interface Props {
  lang?: string;
}

const { lang = 'es' } = Astro.props;
const content = lang === 'es' ? contentEs : await import('../../data/content-en.json').then(m => m.default);
---

<header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center h-16">
      <!-- Logo -->
      <div class="flex items-center">
        <a href="/" class="flex items-center space-x-2 text-xl font-bold text-blue-600 hover:text-blue-700 transition-colors">
          <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
          </svg>
          <span>Murales Santa Marta</span>
        </a>
      </div>

      <!-- Navegación desktop -->
      <nav class="hidden md:flex items-center space-x-8" role="navigation" aria-label="Navegación principal">
        <a href="/" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors">
          {content.navigation.home}
        </a>
        <a href="#audioguides" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors">
          {content.navigation.audioguides}
        </a>
        <a href="#map" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors">
          {content.navigation.map}
        </a>
        <a href="/mapa-murales.pdf" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors" download>
          {content.navigation.download}
        </a>
      </nav>

      <!-- Controles de la derecha -->
      <div class="flex items-center space-x-4">
        <!-- Selector de idioma (temporal) -->
        <div class="flex items-center">
          <a 
            href={lang === 'es' ? '/english' : '/'} 
            class="px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors border border-gray-300 rounded-md"
          >
            {lang === 'es' ? 'EN' : 'ES'}
          </a>
        </div>
        
        <!-- Controles de accesibilidad -->
        <div class="hidden sm:flex items-center space-x-2">
          <button 
            id="toggle-contrast" 
            class="p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors"
            aria-label={content.accessibility.high_contrast}
            title={content.accessibility.high_contrast}
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
            </svg>
          </button>
          
          <div class="flex items-center space-x-1">
            <button 
              id="decrease-font" 
              class="p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors text-sm"
              aria-label={`${content.accessibility.decrease} ${content.accessibility.font_size}`}
              title={`${content.accessibility.decrease} ${content.accessibility.font_size}`}
            >
              A-
            </button>
            <button 
              id="increase-font" 
              class="p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors text-lg"
              aria-label={`${content.accessibility.increase} ${content.accessibility.font_size}`}
              title={`${content.accessibility.increase} ${content.accessibility.font_size}`}
            >
              A+
            </button>
          </div>
        </div>

        <!-- Botón menú móvil -->
        <button 
          id="mobile-menu-button"
          class="md:hidden p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors"
          aria-label={content.accessibility.menu}
          aria-expanded="false"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- Menú móvil -->
    <div id="mobile-menu" class="md:hidden hidden border-t border-gray-200 py-4">
      <nav class="space-y-2" role="navigation" aria-label="Navegación móvil">
        <a href="/" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">
          {content.navigation.home}
        </a>
        <a href="#audioguides" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">
          {content.navigation.audioguides}
        </a>
        <a href="#map" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">
          {content.navigation.map}
        </a>
        <a href="/mapa-murales.pdf" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors" download>
          {content.navigation.download}
        </a>
        
        <!-- Controles de accesibilidad móvil -->
        <div class="pt-4 border-t border-gray-200 space-y-2">
          <button 
            class="w-full text-left px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors"
            onclick="toggleContrast()"
          >
            {content.accessibility.high_contrast}
          </button>
          <div class="flex items-center justify-between px-3 py-2">
            <span class="text-base font-medium text-gray-700">{content.accessibility.font_size}</span>
            <div class="flex items-center space-x-2">
              <button 
                class="px-3 py-1 rounded text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors"
                onclick="decreaseFontSize()"
              >
                A-
              </button>
              <button 
                class="px-3 py-1 rounded text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors"
                onclick="increaseFontSize()"
              >
                A+
              </button>
            </div>
          </div>
        </div>
      </nav>
    </div>
  </div>
</header>

<script>
  // Funcionalidad del menú móvil
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const mobileMenu = document.getElementById('mobile-menu');
  
  if (mobileMenuButton && mobileMenu) {
    mobileMenuButton.addEventListener('click', () => {
      const isExpanded = mobileMenuButton.getAttribute('aria-expanded') === 'true';
      mobileMenuButton.setAttribute('aria-expanded', String(!isExpanded));
      mobileMenu.classList.toggle('hidden');
    });
  }

  // Funcionalidad de accesibilidad
  function toggleContrast() {
    document.body.classList.toggle('high-contrast');
    localStorage.setItem('highContrast', String(document.body.classList.contains('high-contrast')));
  }

  function increaseFontSize() {
    const currentSize = parseInt(document.documentElement.style.fontSize || '16');
    const newSize = Math.min(currentSize + 2, 24);
    document.documentElement.style.fontSize = newSize + 'px';
    localStorage.setItem('fontSize', String(newSize));
  }

  function decreaseFontSize() {
    const currentSize = parseInt(document.documentElement.style.fontSize || '16');
    const newSize = Math.max(currentSize - 2, 12);
    document.documentElement.style.fontSize = newSize + 'px';
    localStorage.setItem('fontSize', String(newSize));
  }

  // Cargar preferencias guardadas
  document.addEventListener('DOMContentLoaded', () => {
    const savedContrast = localStorage.getItem('highContrast');
    const savedFontSize = localStorage.getItem('fontSize');
    
    if (savedContrast === 'true') {
      document.body.classList.add('high-contrast');
    }
    
    if (savedFontSize) {
      document.documentElement.style.fontSize = savedFontSize + 'px';
    }
  });

  // Event listeners para botones desktop
  document.getElementById('toggle-contrast')?.addEventListener('click', toggleContrast);
  document.getElementById('increase-font')?.addEventListener('click', increaseFontSize);
  document.getElementById('decrease-font')?.addEventListener('click', decreaseFontSize);
</script>

<style>
  /* Estilos para alto contraste */
  :global(.high-contrast) {
    filter: contrast(150%) brightness(120%);
  }
  
  :global(.high-contrast) header {
    @apply bg-black text-white border-white;
  }
  
  :global(.high-contrast) a {
    @apply text-yellow-300;
  }
  
  :global(.high-contrast) button {
    @apply text-white bg-black border-white;
  }
</style> 