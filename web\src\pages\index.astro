---
import Layout from '../components/ui/Layout.astro';
import Header from '../components/ui/Header.astro';
import Card from '../components/ui/Card.astro';
import contentEs from '../data/content-es.json';
---

<Layout title={contentEs.pages.title} description={contentEs.pages.description}>
  <!-- Importar estilos globales de Tailwind -->
  <link rel="stylesheet" href="../styles/global.css" />
  
  <Header lang="es" />
  
  <main id="main-content" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Hero Section -->
    <div class="text-center mb-12">
      <h1 class="text-4xl font-bold text-gray-900 mb-4 sm:text-5xl md:text-6xl">
        {contentEs.pages.title}
      </h1>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        {contentEs.pages.description}
      </p>
    </div>

    <!-- Cards Grid Section -->
    <section id="audioguides" class="mb-16" aria-labelledby="audioguides-title">
      <h2 id="audioguides-title" class="text-3xl font-bold text-gray-900 mb-8 text-center">
        {contentEs.navigation.audioguides}
      </h2>
      
      <!-- Grid de cards 2x2 + cards anchas -->
      <div class="grid grid-cols-2 gap-6 max-w-4xl mx-auto">
        <!-- Selector de idioma - Tarjeta ancha -->
        <Card
          title={contentEs.cards.select_language}
          description={contentEs.cards.select_language_desc}
          href="/english"
          variant="wide"
          className="bg-gradient-to-r from-blue-500 to-purple-600 text-white"
        />
        
        <!-- Audioguía Fácil - Tarjeta destacada -->
        <Card
          title={contentEs.audioguide_types.easy}
          description="Diseñada especialmente para personas con discapacidad intelectual"
          href="/audioguia-facil"
          variant="wide"
          priority={true}
          image="/images/audioguia-facil.jpg"
          alt="Interfaz simplificada de la audioguía fácil"
        />
        
        <!-- Audioguía Normativa -->
        <Card
          title={contentEs.audioguide_types.normal}
          description="Audio estándar con información detallada"
          href="/audioguia-normativa"
          image="/images/audioguia-normal.jpg"
          alt="Reproductor de audio estándar"
        />
        
        <!-- Audioguía Descriptiva -->
        <Card
          title={contentEs.audioguide_types.descriptive}
          description="Con descripciones detalladas para mayor accesibilidad"
          href="/audioguia-descriptiva"
          image="/images/audioguia-descriptiva.jpg"
          alt="Reproductor con controles de accesibilidad avanzada"
        />
        
        <!-- Signoguía -->
        <Card
          title={contentEs.audioguide_types.sign}
          description="Videos en lengua de signos española"
          href="/signoguia"
          image="/images/signoguia.jpg"
          alt="Intérprete de lengua de signos"
        />
        
        <!-- Mapa de Ruta - Tarjeta ancha -->
        <Card
          title={contentEs.pages.route_title}
          description={contentEs.pages.route_description}
          href="#map"
          variant="wide"
          image="/images/mapa-ruta.jpg"
          alt="Mapa con la ruta recomendada de murales"
        />
      </div>
    </section>

    <!-- Mapa Section -->
    <section id="map" class="mb-16" aria-labelledby="map-title">
      <h2 id="map-title" class="text-3xl font-bold text-gray-900 mb-8 text-center">
        {contentEs.navigation.map}
      </h2>
      
      <div class="bg-white rounded-xl shadow-lg p-6">
        <div class="aspect-[16/9] bg-gray-200 rounded-lg flex items-center justify-center">
          <div class="text-center text-gray-500">
            <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
            </svg>
            <p class="text-lg font-medium">Mapa Interactivo</p>
            <p class="text-sm">El mapa se cargará aquí con los componentes React</p>
          </div>
        </div>
        
        <div class="mt-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div class="mb-4 sm:mb-0">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Información de la Ruta</h3>
            <div class="space-y-1 text-sm text-gray-600">
              <p><strong>Distancia total:</strong> 2.5 km</p>
              <p><strong>Tiempo estimado:</strong> 45 min</p>
              <p><strong>Número de murales:</strong> 3</p>
            </div>
          </div>
          
          <div class="flex flex-col sm:flex-row gap-3">
            <a 
              href="https://maps.google.com/maps?daddr=11.2408,-74.2120" 
              target="_blank"
              rel="noopener noreferrer"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm font-medium"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
              </svg>
              {contentEs.pages.directions}
            </a>
            
            <button class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"/>
              </svg>
              Descargar PDF
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- Info adicional -->
    <section class="bg-gray-50 rounded-xl p-8 text-center">
      <h2 class="text-2xl font-bold text-gray-900 mb-4">¿Necesitas ayuda?</h2>
      <p class="text-gray-600 mb-6 max-w-2xl mx-auto">
        Todas nuestras audioguías están diseñadas para ser accesibles. Si tienes alguna dificultad o necesitas asistencia adicional, no dudes en contactarnos.
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a 
          href="tel:+573001234567" 
          class="inline-flex items-center px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors font-medium"
        >
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
          </svg>
          Llamar ahora
        </a>
        <a 
          href="mailto:<EMAIL>" 
          class="inline-flex items-center px-6 py-3 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors font-medium"
        >
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
          </svg>
          Enviar email
        </a>
      </div>
    </section>
  </main>

  <!-- Footer simple -->
  <footer class="bg-gray-900 text-white py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div>
          <h3 class="text-lg font-semibold mb-4">Murales Santa Marta</h3>
          <p class="text-gray-300 text-sm">
            Descubre la historia y el arte de Santa Marta a través de nuestras audioguías accesibles.
          </p>
        </div>
        
        <div>
          <h3 class="text-lg font-semibold mb-4">Enlaces rápidos</h3>
          <ul class="space-y-2 text-sm">
            <li><a href="/" class="text-gray-300 hover:text-white transition-colors">Inicio</a></li>
            <li><a href="#audioguides" class="text-gray-300 hover:text-white transition-colors">Audioguías</a></li>
            <li><a href="#map" class="text-gray-300 hover:text-white transition-colors">Mapa</a></li>
            <li><a href="/english" class="text-gray-300 hover:text-white transition-colors">English</a></li>
          </ul>
        </div>
        
        <div>
          <h3 class="text-lg font-semibold mb-4">Accesibilidad</h3>
          <p class="text-gray-300 text-sm mb-4">
            Comprometidos con hacer la cultura accesible para todos.
          </p>
          <div class="flex space-x-4">
            <span class="text-xs bg-gray-700 px-2 py-1 rounded">WCAG 2.1 AA</span>
            <span class="text-xs bg-gray-700 px-2 py-1 rounded">ISO 14289</span>
          </div>
        </div>
      </div>
      
      <div class="border-t border-gray-700 mt-8 pt-8 text-center text-sm text-gray-400">
        <p>&copy; 2025 Murales Santa Marta. Todos los derechos reservados.</p>
      </div>
    </div>
  </footer>
</Layout>
