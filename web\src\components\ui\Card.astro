---
export interface Props {
  title: string;
  description?: string;
  href?: string;
  image?: string;
  alt?: string;
  variant?: 'square' | 'wide';
  className?: string;
  priority?: boolean; // Para audioguía fácil
}

const { 
  title, 
  description, 
  href, 
  image, 
  alt, 
  variant = 'square', 
  className = '',
  priority = false 
} = Astro.props;

const cardClasses = `
  group relative overflow-hidden rounded-xl shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105 focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2
  ${variant === 'wide' ? 'col-span-2 aspect-[2/1]' : 'aspect-square'}
  ${priority ? 'ring-2 ring-yellow-400 bg-yellow-50' : 'bg-white'}
  ${className}
`;

const ContentWrapper = href ? 'a' : 'div';
---

<div class={cardClasses}>
  <ContentWrapper 
    href={href}
    class={`block h-full w-full focus:outline-none ${href ? 'cursor-pointer' : ''}`}
    role={href ? 'link' : 'presentation'}
    aria-label={href ? `${title}${description ? ': ' + description : ''}` : undefined}
  >
    {image && (
      <div class="absolute inset-0">
        <img 
          src={image} 
          alt={alt || title}
          class="h-full w-full object-cover transition-transform duration-300 group-hover:scale-110"
          loading="lazy"
        />
        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
      </div>
    )}
    
    <div class={`relative h-full flex flex-col justify-end p-6 ${image ? 'text-white' : 'text-gray-900'}`}>
      {priority && (
        <div class="absolute top-4 right-4">
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-400 text-yellow-900">
            Destacado
          </span>
        </div>
      )}
      
      <h3 class={`font-bold text-lg mb-2 ${image ? 'text-white' : priority ? 'text-gray-900' : 'text-gray-900'}`}>
        {title}
      </h3>
      
      {description && (
        <p class={`text-sm opacity-90 ${image ? 'text-gray-100' : priority ? 'text-gray-700' : 'text-gray-600'}`}>
          {description}
        </p>
      )}
      
      {href && (
        <div class="mt-4 flex items-center text-sm font-medium">
          <span class={image ? 'text-white' : 'text-blue-600'}>
            Ver más
          </span>
          <svg class="ml-2 w-4 h-4 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
          </svg>
        </div>
      )}
    </div>
    
    <!-- Indicador de enfoque para accesibilidad -->
    <div class="absolute inset-0 rounded-xl opacity-0 focus-within:opacity-100 ring-2 ring-blue-500 pointer-events-none transition-opacity"></div>
  </ContentWrapper>
</div>

<style>
  /* Mejoras para alto contraste */
  :global(.high-contrast) .group {
    @apply border-2 border-white;
  }
  
  :global(.high-contrast) .group:hover {
    @apply border-yellow-400;
  }
  
  /* Reducir animaciones si el usuario lo prefiere */
  @media (prefers-reduced-motion: reduce) {
    .group {
      transition: none;
    }
    
    .group img {
      transition: none;
    }
    
    .group:hover {
      transform: none;
    }
    
    .group:hover img {
      transform: none;
    }
  }
</style> 