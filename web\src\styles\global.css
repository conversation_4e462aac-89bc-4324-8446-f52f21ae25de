@import "tailwindcss";

/* Configuración de modo oscuro manual para Tailwind CSS 4 */
@custom-variant dark (&:where(.dark, .dark *));

/* Configuración de Tailwind CSS 4 */
@theme {
  /* Colores personalizados del manual de identidad */
  --color-SM-blue: #0072c0;
  --color-SM-yellow: #f8c200;
  --color-SM-gray: #c3c5c5;
  --color-SM-black: #282828;
  
  /* Fuentes */
  --font-family-nunito: "Nunito", system-ui, sans-serif;
  --font-family-sans: "Nunito", system-ui, sans-serif;
  
  /* Tamaños de fuente fluidos */
  --font-size-fluid-xl: clamp(1.75rem, 4vw, 2.25rem);
  --font-size-fluid-lg: clamp(1.5rem, 3vw, 1.875rem);
  --font-size-fluid-base: clamp(1rem, 2vw, 1.125rem);
  --font-size-fluid-sm: clamp(0.875rem, 1.5vw, 1rem);
  
  /* Espaciado personalizado */
  --spacing-18: 4.5rem;
  --spacing-88: 22rem;
  
  /* Border radius personalizado */
  --radius-xl: 12px;
  --radius-2xl: 16px;
  
  /* Animaciones */
  --animate-fade-in: fadeIn 0.5s ease-in-out;
  --animate-slide-up: slideUp 0.3s ease-out;
  --animate-pulse-gentle: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Fuentes Nunito */
@font-face {
  font-family: 'Nunito';
  src: url('/fonts/Nunito-VariableFont_wght.ttf') format('truetype-variations');
  font-weight: 200 1000;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('/fonts/Nunito-Italic-VariableFont_wght.ttf') format('truetype-variations');
  font-weight: 200 1000;
  font-style: italic;
  font-display: swap;
}

/* Keyframes para animaciones */
@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% { transform: translateY(10px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

/* Configuración de fuentes responsivas fluidas */
.text-fluid-xl { font-size: clamp(1.75rem, 4vw, 2.25rem); }
.text-fluid-lg { font-size: clamp(1.5rem, 3vw, 1.875rem); }
.text-fluid-base { font-size: clamp(1rem, 2vw, 1.125rem); }
.text-fluid-sm { font-size: clamp(0.875rem, 1.5vw, 1rem); }

/* Clases de accesibilidad del manual de identidad */

/* Alto contraste */
.high-contrast {
  --tw-text-slate-900: #000000;
  --tw-text-slate-100: #ffffff;
  --tw-bg-white: #ffffff;
  --tw-bg-slate-900: #000000;
  --tw-border-slate-200: #000000;
}

.high-contrast button, .high-contrast .card {
  border: 2px solid currentColor !important;
}

/* Reducción de movimiento */
.reduce-motion *,
.reduce-motion *::before,
.reduce-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
}

/* Focus visible mejorado */
.focus-visible:focus {
  outline: 2px solid #1E40AF;
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(30, 64, 175, 0.1);
}

/* Skip links */
.skip-link {
  position: absolute;
  left: -9999px;
  top: 0;
  z-index: 999;
  padding: 8px 16px;
  background: #1E40AF;
  color: white;
  text-decoration: none;
  font-weight: 600;
}

.skip-link:focus {
  left: 6px;
  top: 6px;
}

/* Estilos base para el proyecto */
* {
  box-sizing: border-box;
}

html {
  font-family: 'Nunito', system-ui, sans-serif;
  line-height: 1.6;
  transition: background-color 0.3s ease, color 0.3s ease;
}

body {
  margin: 0;
  background-color: #f8fafc; /* slate-50 */
  color: #0f172a; /* slate-900 */
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Modo oscuro */
.dark body {
  background-color: #0f172a; /* slate-900 */
  color: #f1f5f9; /* slate-100 */
}

/* Iconos del modo oscuro */
.sun-icon {
  display: none;
}

.moon-icon {
  display: block;
}

.dark .sun-icon {
  display: block;
}

.dark .moon-icon {
  display: none;
}

/* Transiciones suaves para elementos interactivos */
button, 
.card,
header,
footer {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* Mejor contraste para enlaces en modo oscuro */
.dark a {
  color: #60a5fa; /* blue-400 */
}

.dark a:hover {
  color: #93c5fd; /* blue-300 */
}

/* Estados de hover mejorados */
.dark .hover\:bg-slate-100:hover {
  background-color: #334155 !important; /* slate-700 */
}

.dark .hover\:bg-slate-200:hover {
  background-color: #475569 !important; /* slate-600 */
} 